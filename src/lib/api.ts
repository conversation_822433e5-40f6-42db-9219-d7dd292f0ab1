import { notifications } from './stores/notifications.js';
import { auth } from './stores/auth.js';
import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import {PUBLIC_API_BASE_URL} from "$env/static/public";

interface ApiResponse {
	status: string;
	message: string;
	data: any;
}

class ApiClient {
	private readonly baseUrl: string;

	constructor() {
		this.baseUrl = PUBLIC_API_BASE_URL ?? '/api/v1/e-ad/sys-arch';
	}

	private getCsrf(): string | undefined {
		if (!browser) return undefined;
		
		return document.cookie
			.split('; ')
			.find(c => c.startsWith('csrf_token='))
			?.split('=')[1];
	}

	private getHeaders(): Record<string, string> {
		const headers: Record<string, string> = {
			'Content-Type': 'application/json',
		};

		const token = this.getCsrf();
		if (token) {
			headers['X-CSRF-Token'] = token;
		}

		return headers;
	}

	private async request(endpoint: string, options: RequestInit = {}): Promise<ApiResponse> {
		const config: RequestInit = {
			headers: {
				...this.getHeaders(),
				...options.headers,
			},
			...options,
			credentials: 'include',
		};

		const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;
        const response = await fetch(url, config);

        let data;
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('application/json')) {
            data = await response.json();
        } else {
            data = await response.text();
        }

        if (!response.ok) {
            // Handle session expired
            if (response.status === 909) {
                this.handleSessionExpired();
                throw new Error('Session expired');
            }

            const msg = typeof data === 'object' && data.message
                ? data.message
                : `HTTP ${response.status}: ${response.statusText}`;

            if (data?.status === 'warning') {
                notifications.warning(msg);
            } else {
                notifications.error(msg);
            }

            throw new Error(msg);
        }

        return data;
    }

	private handleSessionExpired() {
		if (!browser) return;
		
		// Store current page for redirect after login
		const currentPath = window.location.pathname + window.location.search;
		if (!currentPath.includes('/login') && !currentPath.includes('/signup')) {
			localStorage.setItem('auth_redirect_url', currentPath);
		}
		
		// Update auth store
		auth.sessionExpired();
		
		// Show notification
		notifications.warning('Your session has expired. Please log in again.');
		
		// Redirect to login
		setTimeout(() => {
			goto('/login');
		}, 1500);
	}

	// HTTP methods
	async get(endpoint: string): Promise<ApiResponse> {
		return this.request(endpoint);
	}

	async post(endpoint: string, data: any): Promise<ApiResponse> {
		return this.request(endpoint, {
			method: 'POST',
			body: JSON.stringify(data),
		});
	}

	async put(endpoint: string, data?: any): Promise<ApiResponse> {
		return this.request(endpoint, {
			method: 'PUT',
			body: data ? JSON.stringify(data) : undefined,
		});
	}

	async patch(endpoint: string, data?: any): Promise<ApiResponse> {
		return this.request(endpoint, {
			method: 'PATCH',
			body: data ? JSON.stringify(data) : undefined,
		});
	}

	async delete(endpoint: string): Promise<ApiResponse> {
		return this.request(endpoint, {
			method: 'DELETE',
		});
	}

	// Auth methods
	async logout(): Promise<void> {
		try {
			await this.delete('/auth/logout');
		} catch (error) {
			console.warn('Server logout failed, continuing with client-side logout');
		}
		
		auth.logout();
		
		if (browser) {
			goto('/login');
		}
	}
}

export const api = new ApiClient();
