<script lang="ts">
	import { cn } from '../utils.js';
	import { LoaderCircle } from '@lucide/svelte';

	let {
		variant = 'default',
		size = 'default',
		loading = false,
		loadingText,
		disabled = false,
		type = 'button',
		class: className = '',
		onclick,
		children,
		...restProps
	}: {
        variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost';
        size?: 'default' | 'sm' | 'lg' | 'icon';
        loading?: boolean;
        loadingText?: string;
        [attr: string]: any;
    } = $props();

	const variants = {
		default: 'bg-primary text-primary-foreground hover:bg-primary/90',
		destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
		outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
		secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
		ghost: 'hover:bg-accent hover:text-accent-foreground'
	};

	const sizes = {
		default: 'h-10 px-4 py-2',
		sm: 'h-9 rounded-md px-3',
		lg: 'h-11 rounded-md px-8',
		icon: 'h-10 w-10'
	};

	const baseClasses = 'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-pointer';
</script>

<button
	{type}
	class={cn(baseClasses, variants[variant], sizes[size], className)}
	disabled={disabled || loading}
	onclick={onclick}
	{...restProps}
>
	{#if loading}
		<LoaderCircle class="mr-2 h-4 w-4 animate-spin" />
		{loadingText || 'Loading...'}
	{:else}
		{@render children?.()}
	{/if}
</button>

<style>
	:global(.animate-spin) {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
</style>
