<script lang="ts">
	import { cn } from '../utils.js';

	let {
		class: className = '',
		title,
		titleClass = '',
		description,
		descriptionClass = '',
		headerClass = '',
		contentClass = '',
		children
	}: {
        title?: string;
        titleClass?: string;
        description?: string;
        descriptionClass?: string;
        headerClass?: string;
        contentClass?: string;

        [attr: string]: any
    } = $props();

	const hasHeader = title || description;
</script>

<div class={cn('rounded-lg border bg-card text-card-foreground shadow-sm', className)}>
	{#if hasHeader}
		<div class={cn('flex flex-col space-y-1.5 p-6', headerClass)}>
			{#if title}
				<h3 class={cn('text-2xl font-semibold leading-none tracking-tight', titleClass)}>
					{title}
				</h3>
			{/if}
			{#if description}
				<p class={cn('text-sm text-muted-foreground', descriptionClass)}>
					{description}
				</p>
			{/if}
		</div>
	{/if}

	{#if children}
		<div class={cn('p-6', hasHeader ? 'pt-0' : '', contentClass)}>
			{@render children?.()}
		</div>
	{/if}
</div>
