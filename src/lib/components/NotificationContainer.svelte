<script lang="ts">
	import { notifications, type Notification } from '../stores/notifications.js';
	import { CheckCircle, XCircle, AlertTriangle, Info, X } from '@lucide/svelte';
	import { cn } from '../utils.js';
	import Button from './Button.svelte';

	const notificationList = notifications;

	function getIcon(type: Notification['type']) {
		switch (type) {
			case 'success': return CheckCircle;
			case 'error': return XCircle;
			case 'warning': return AlertTriangle;
			case 'info': return Info;
		}
	}

	function getStyles(type: Notification['type']) {
		switch (type) {
			case 'success': return 'bg-green-50 border-green-200 text-green-800 dark:bg-green-950 dark:border-green-800 dark:text-green-200';
			case 'error': return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-950 dark:border-red-800 dark:text-red-200';
			case 'warning': return 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-950 dark:border-yellow-800 dark:text-yellow-200';
			case 'info': return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950 dark:border-blue-800 dark:text-blue-200';
		}
	}

	function getIconColor(type: Notification['type']) {
		switch (type) {
			case 'success': return 'text-green-600 dark:text-green-400';
			case 'error': return 'text-red-600 dark:text-red-400';
			case 'warning': return 'text-yellow-600 dark:text-yellow-400';
			case 'info': return 'text-blue-600 dark:text-blue-400';
		}
	}
</script>

<div class="fixed top-4 right-4 z-50 flex flex-col gap-2 pointer-events-none max-w-sm">
	{#each $notificationList as notification (notification.id)}
		<div
			class={cn(
				'relative flex items-start gap-3 rounded-lg border p-4 shadow-lg pointer-events-auto animate-in slide-in-from-right-full',
				getStyles(notification.type)
			)}
			role="alert"
		>
			<!-- Icon -->
			<div class="flex-shrink-0 mt-0.5">
				{#if getIcon(notification.type)}
					<svelte:component this={getIcon(notification.type)} class={cn('h-5 w-5', getIconColor(notification.type))} />
				{/if}
			</div>

			<!-- Content -->
			<div class="flex-1 min-w-0">
				{#if notification.title}
					<div class="text-sm font-semibold mb-1">
						{notification.title}
					</div>
				{/if}
				<div class="text-sm">
					{notification.message}
				</div>
				
				{#if notification.action}
					<div class="mt-2">
						<Button
							variant="outline"
							size="sm"
							class="h-7 px-2 text-xs"
							onclick={notification.action.onClick}
						>
							{notification.action.label}
						</Button>
					</div>
				{/if}
			</div>

			<!-- Close Button -->
			<Button
				variant="ghost"
				size="icon"
				class="flex-shrink-0 h-6 w-6 p-0 hover:bg-background/80"
				onclick={() => notifications.remove(notification.id)}
				aria-label="Close notification"
			>
				<X class="h-4 w-4" />
			</Button>
		</div>
	{/each}
</div>

<style>
	@keyframes slide-in-from-right-full {
		from {
			transform: translateX(100%);
		}
		to {
			transform: translateX(0);
		}
	}

	.animate-in {
		animation-duration: 0.3s;
		animation-timing-function: ease-out;
		animation-fill-mode: both;
	}

	.slide-in-from-right-full {
		animation-name: slide-in-from-right-full;
	}
</style>
