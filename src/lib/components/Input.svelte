<script lang="ts">
	import { cn } from '../utils.js';
	import { Eye, EyeOff } from '@lucide/svelte';

	interface Props {
		type?: string;
		name?: string;
		label?: string;
		placeholder?: string;
		value?: string;
		disabled?: boolean;
		required?: boolean;
		class?: string;
		formData?: Record<string, any>;
		oninput?: (event: Event) => void;
	}

	let {
		type = 'text',
		name,
		label,
		placeholder,
		value = '',
		disabled = false,
		required = false,
		class: className = '',
		formData,
		oninput,
		...restProps
	}: Props = $props();

	let showPassword = $state(false);
	let inputElement: HTMLInputElement;

	const isPasswordType = type === 'password';
	const inputType = $derived(isPasswordType ? (showPassword ? 'text' : 'password') : type);

	// Get value from formData if available
	const inputValue = $derived(formData && name ? (formData[name] ?? '') : value);

	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		const newValue = target.value;
		
		// Update formData if provided
		if (formData && name) {
			formData[name] = newValue;
		}
		
		// Call custom oninput handler
		if (oninput) {
			oninput(event);
		}
	}

	function togglePasswordVisibility() {
		showPassword = !showPassword;
	}

	const id = name || label || '';
</script>

<div class="space-y-2">
	{#if label}
		<label for={id} class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
			{label}
			{#if required}
				<span class="text-destructive">*</span>
			{/if}
		</label>
	{/if}
	
	<div class="relative">
		<input
			bind:this={inputElement}
			{id}
			{name}
			type={inputType}
			{placeholder}
			value={inputValue}
			{disabled}
			{required}
			class={cn(
				'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
				isPasswordType && 'pr-10',
				className
			)}
			oninput={handleInput}
			{...restProps}
		/>
		
		{#if isPasswordType}
			<button
				type="button"
				class="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
				onclick={togglePasswordVisibility}
				aria-label={showPassword ? 'Hide password' : 'Show password'}
			>
				{#if showPassword}
					<EyeOff class="h-4 w-4" />
				{:else}
					<Eye class="h-4 w-4" />
				{/if}
			</button>
		{/if}
	</div>
</div>
