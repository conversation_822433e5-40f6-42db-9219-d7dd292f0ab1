<script lang="ts">
	import { notifications, type Notification } from '../stores/notifications.js';
	import { Check<PERSON>ircle, XCircle, AlertTriangle, Info, X } from '@lucide/svelte';
	import { cn } from '../utils.js';
	import Button from './Button.svelte';

    type DialogType = 'default' | 'confirm' | 'alert' | 'prompt'

    interface DialogContextType {
        dialogs: DialogInstance[]
        openDialog: (config: DialogConfig) => string
        closeDialog: (id: string) => void
        closeAll: () => void
    }

    interface DialogConfig {
        id?: string
        type?: DialogType
        title?: string
        description?: string
        content?: HTMLElement
        size?: 'sm' | 'default' | 'lg' | 'xl'
        variant?: 'default'
        showCloseButton?: boolean
        persistent?: boolean
        onConfirm?: () => void | Promise<void>
        onCancel?: () => void
        onClose?: () => void
        confirmLabel?: string
        cancelLabel?: string
        confirmVariant?: 'default' | 'destructive' | 'outline' | 'secondary'
    }

    interface DialogInstance {
        id: string
        config: DialogConfig
        isOpen: boolean
    }

    let globalDialogInstance: DialogContextType | null = null;

    const dialog = {
        // Basic dialog
        open: (config: DialogConfig): string => {
            if (!globalDialogInstance) {
                console.warn('DialogProvider not found. Please wrap your app with DialogProvider.')
                return ''
            }
            return globalDialogInstance.openDialog(config)
        },

        // Confirmation dialog
        confirm: (title: string, description?: string, options?: Partial<DialogConfig>): Promise<boolean> => {
            return new Promise((resolve) => {
                const id = dialog.open({
                    type: 'confirm',
                    title,
                    description,
                    confirmLabel: 'Confirm',
                    cancelLabel: 'Cancel',
                    onConfirm: () => {
                        dialog.close(id)
                        resolve(true)
                    },
                    onCancel: () => {
                        dialog.close(id)
                        resolve(false)
                    },
                    ...options
                })
            })
        },

        // Alert dialog
        alert: (title: string, description?: string, options?: Partial<DialogConfig>): Promise<void> => {
            return new Promise((resolve) => {
                const id = dialog.open({
                    type: 'alert',
                    title,
                    description,
                    confirmLabel: 'OK',
                    onConfirm: () => {
                        dialog.close(id)
                        resolve()
                    },
                    ...options
                })
            })
        },

        // Close specific dialog
        close: (id: string) => {
            if (globalDialogInstance) {
                globalDialogInstance.closeDialog(id)
            }
        },

        // Close all dialogs
        closeAll: () => {
            if (globalDialogInstance) {
                globalDialogInstance.closeAll()
            }
        }
    }

    const handleClose = () => {
        if (config.onClose) {
            config.onClose()
        }
        onClose(dialog.id)
    }

</script>

