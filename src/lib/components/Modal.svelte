<script lang="ts">
	import { X } from '@lucide/svelte';
	import { cn } from '../utils.js';
	import Button from './Button.svelte';
	import type { Modal } from '../stores/modals.js';

	interface Props {
		modal: Modal;
		onClose: (id: string) => void;
	}

	let { modal, onClose }: Props = $props();

	const { config } = modal;

	function getSizeClasses(size: string) {
		switch (size) {
			case 'sm': return 'max-w-sm';
			case 'lg': return 'max-w-2xl';
			case 'xl': return 'max-w-4xl';
			default: return 'max-w-md';
		}
	}

	function handleBackdropClick() {
		if (!config.persistent)
			handleClose();
	}

	function handleClose() {
		if (config.onClose) {
			config.onClose();
		}
		onClose(modal.id);
	}

	function handleConfirm() {
		if (config.onConfirm) {
			const result = config.onConfirm();
			if (result instanceof Promise) {
				result.then(() => {
					if (config.type !== 'prompt') {
						onClose(modal.id);
					}
				}).catch(() => {
					// Handle error if needed
				});
			} else {
				if (config.type !== 'prompt') {
					onClose(modal.id);
				}
			}
		} else {
			onClose(modal.id);
		}
	}

	function handleCancel() {
		if (config.onCancel) {
			config.onCancel();
		}
		onClose(modal.id);
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && !config.persistent) {
			handleClose();
		}
	}
</script>

<svelte:window on:keydown={handleKeydown} />

<!-- Backdrop -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div
    tabindex="-1"
	class="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
	onclick={handleBackdropClick}
	role="dialog"
	aria-modal="true"
	aria-labelledby={config.title ? `modal-title-${modal.id}` : undefined}
	aria-describedby={config.description ? `modal-description-${modal.id}` : undefined}
>
	<!-- Modal -->
	<div class="fixed inset-0 z-50 flex items-center justify-center p-4">
		<div class={cn(
			'relative w-full rounded-lg border bg-background shadow-lg animate-in fade-in-0 zoom-in-95',
			getSizeClasses(config.size || 'default')
		)}>
			<!-- Header -->
			{#if config.title || config.showCloseButton}
				<div class="flex items-center justify-between p-6 pb-4">
					{#if config.title}
						<h2 id="modal-title-{modal.id}" class="text-lg font-semibold leading-none tracking-tight">
							{config.title}
						</h2>
					{/if}
					{#if config.showCloseButton && !config.persistent}
						<button
							type="button"
							class="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
							onclick={handleClose}
							aria-label="Close modal"
						>
							<X class="h-4 w-4" />
						</button>
					{/if}
				</div>
			{/if}

			<!-- Content -->
			<div class="px-6 {config.title || config.showCloseButton ? '' : 'pt-6'}">
				{#if config.description}
					<p id="modal-description-{modal.id}" class="text-sm text-muted-foreground mb-4">
						{config.description}
					</p>
				{/if}

				{#if config.content}
					<div class="mb-4">
						{@html config.content}
					</div>
				{/if}
			</div>

			<!-- Footer -->
			{#if config.type === 'confirm' || config.type === 'alert' || config.type === 'prompt'}
				<div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 p-6 pt-4">
					{#if config.type === 'confirm' || config.type === 'prompt'}
						<Button
							variant={config.cancelVariant || 'outline'}
							onclick={handleCancel}
							class="mt-2 sm:mt-0"
						>
							{config.cancelLabel || 'Cancel'}
						</Button>
					{/if}
					<Button
						variant={config.confirmVariant || 'default'}
						onclick={handleConfirm}
					>
						{config.confirmLabel || 'OK'}
					</Button>
				</div>
			{/if}
		</div>
	</div>
</div>

