import { writable } from 'svelte/store';
import { generateId } from '../utils.js';

export type ModalType = 'default' | 'confirm' | 'alert' | 'prompt';
export type ModalSize = 'sm' | 'default' | 'lg' | 'xl';
export type ModalVariant = 'default' | 'destructive';

export interface ModalConfig {
	id?: string;
	type?: ModalType;
	title?: string;
	description?: string;
	content?: string;
	size?: ModalSize;
	variant?: ModalVariant;
	showCloseButton?: boolean;
	persistent?: boolean;
	onConfirm?: () => void | Promise<void>;
	onCancel?: () => void;
	onClose?: () => void;
	confirmLabel?: string;
	cancelLabel?: string;
	confirmVariant?: 'default' | 'destructive' | 'outline' | 'secondary';
	cancelVariant?: 'default' | 'destructive' | 'outline' | 'secondary';
}

export interface Modal {
	id: string;
	config: ModalConfig;
	isOpen: boolean;
	resolve?: (value: any) => void;
	reject?: (reason?: any) => void;
}

function createModalStore() {
	const { subscribe, update } = writable<Modal[]>([]);

	const addModal = (config: ModalConfig): string => {
		const id = config.id || generateId();
		const modal: Modal = {
			id,
			config: {
				type: 'default',
				size: 'default',
				variant: 'default',
				showCloseButton: true,
				persistent: false,
				confirmLabel: 'OK',
				cancelLabel: 'Cancel',
				confirmVariant: 'default',
				cancelVariant: 'outline',
				...config,
				id
			},
			isOpen: true
		};

		update(modals => [...modals, modal]);
		return id;
	};

	const removeModal = (id: string) => {
		update(modals => {
			const modal = modals.find(m => m.id === id);
			if (modal?.config.onClose) {
				modal.config.onClose();
			}
			return modals.filter(m => m.id !== id);
		});
	};

	const clearAll = () => {
		update(modals => {
			modals.forEach(modal => {
				if (modal.config.onClose) {
					modal.config.onClose();
				}
			});
			return [];
		});
	};

	const updateModal = (id: string, updates: Partial<Modal>) => {
		update(modals => 
			modals.map(modal => 
				modal.id === id ? { ...modal, ...updates } : modal
			)
		);
	};

	return {
		subscribe,
		add: addModal,
		remove: removeModal,
		clear: clearAll,
		update: updateModal,

		// Basic modal
		open: (config: ModalConfig): string => {
			return addModal(config);
		},

		// Confirmation modal
		confirm: (title: string, description?: string, options?: Partial<ModalConfig>): Promise<boolean> => {
			return new Promise((resolve) => {
				const id = addModal({
					type: 'confirm',
					title,
					description,
					confirmLabel: 'Confirm',
					cancelLabel: 'Cancel',
					onConfirm: () => {
						removeModal(id);
						resolve(true);
					},
					onCancel: () => {
						removeModal(id);
						resolve(false);
					},
					onClose: () => {
						resolve(false);
					},
					...options
				});
			});
		},

		// Alert modal
		alert: (title: string, description?: string, options?: Partial<ModalConfig>): Promise<void> => {
			return new Promise((resolve) => {
				const id = addModal({
					type: 'alert',
					title,
					description,
					confirmLabel: 'OK',
					onConfirm: () => {
						removeModal(id);
						resolve();
					},
					onClose: () => {
						resolve();
					},
					...options
				});
			});
		},

		// Prompt modal (for future use)
		prompt: (title: string, description?: string, defaultValue?: string, options?: Partial<ModalConfig>): Promise<string | null> => {
			return new Promise((resolve) => {
				const id = addModal({
					type: 'prompt',
					title,
					description,
					confirmLabel: 'OK',
					cancelLabel: 'Cancel',
					onConfirm: () => {
						// This would need to be handled in the component
						removeModal(id);
						resolve(defaultValue || '');
					},
					onCancel: () => {
						removeModal(id);
						resolve(null);
					},
					onClose: () => {
						resolve(null);
					},
					...options
				});
			});
		},

		// Close specific modal
		close: (id: string) => {
			removeModal(id);
		},

		// Close all modals
		closeAll: () => {
			clearAll();
		}
	};
}

export const modals = createModalStore();
