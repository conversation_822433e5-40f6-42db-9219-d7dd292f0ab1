import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export interface User {
	id: string;
	email: string;
	name: string;
	role: string;
}

interface AuthState {
	user: User | null;
	isLoading: boolean;
}

function createAuthStore() {
	const { subscribe, set, update } = writable<AuthState>({
		user: null,
		isLoading: true
	});

	return {
		subscribe,
		
		// Initialize auth state from localStorage
		init: () => {
			if (!browser) return;
			
			try {
				const storedUser = localStorage.getItem('user_obj');
				if (storedUser) {
					const userData = JSON.parse(storedUser);
					set({ user: userData, isLoading: false });
				} else {
					set({ user: null, isLoading: false });
				}
			} catch (error) {
				console.error('Error loading user from localStorage:', error);
				localStorage.removeItem('user_obj');
				set({ user: null, isLoading: false });
			}
		},

		// Login user
		login: (userData: User) => {
			if (browser) {
				localStorage.setItem('user_obj', JSON.stringify(userData));
			}
			set({ user: userData, isLoading: false });
		},

		// Logout user
		logout: () => {
			if (browser) {
				localStorage.removeItem('user_obj');
				// Store current page for redirect after login
				const currentPath = window.location.pathname + window.location.search;
				if (!currentPath.includes('/login') && !currentPath.includes('/signup')) {
					localStorage.setItem('auth_redirect_url', currentPath);
				}
			}
			set({ user: null, isLoading: false });
		},

		// Handle session expired
		sessionExpired: () => {
			if (browser) {
				localStorage.removeItem('user_obj');
				// Store current page for redirect after login
				const currentPath = window.location.pathname + window.location.search;
				if (!currentPath.includes('/login') && !currentPath.includes('/signup')) {
					localStorage.setItem('auth_redirect_url', currentPath);
				}
			}
			set({ user: null, isLoading: false });
		}
	};
}

export const auth = createAuthStore();
