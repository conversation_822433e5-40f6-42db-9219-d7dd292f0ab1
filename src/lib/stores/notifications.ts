import { writable } from 'svelte/store';
import { generateId } from '../utils.js';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
	id: string;
	type: NotificationType;
	title?: string;
	message: string;
	persistent?: boolean;
	action?: {
		label: string;
		onClick: () => void;
	};
	duration?: number;
}

function createNotificationStore() {
	const { subscribe, update } = writable<Notification[]>([]);

	const addNotification = (notification: Omit<Notification, 'id'>) => {
		const id = generateId();
		const newNotification: Notification = {
			id,
			duration: 5000,
			...notification
		};

		update(notifications => [...notifications, newNotification]);

		// Auto-remove after duration (unless persistent)
		if (!newNotification.persistent && newNotification.duration) {
			setTimeout(() => {
				removeNotification(id);
			}, newNotification.duration);
		}

		return id;
	};

	const removeNotification = (id: string) => {
		update(notifications => notifications.filter(n => n.id !== id));
	};

	const clearAll = () => {
		update(() => []);
	};

	return {
		subscribe,
		add: addNotification,
		remove: removeNotification,
		clear: clearAll,
		
		// Convenience methods
		success: (message: string, options?: Partial<Notification>) => 
			addNotification({ type: 'success', message, ...options }),
		
		error: (message: string, options?: Partial<Notification>) => 
			addNotification({ type: 'error', message, ...options }),
		
		warning: (message: string, options?: Partial<Notification>) => 
			addNotification({ type: 'warning', message, ...options }),
		
		info: (message: string, options?: Partial<Notification>) => 
			addNotification({ type: 'info', message, ...options }),
		
		// Direct replacement for alert()
		alert: (message: string) => 
			addNotification({ type: 'info', message, persistent: true })
	};
}

export const notifications = createNotificationStore();
