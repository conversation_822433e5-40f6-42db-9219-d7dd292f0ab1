<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';
	import { auth } from '$lib/stores/auth.js';
	import { notifications } from '$lib/stores/notifications.js';
	import { api } from '$lib/api.js';
	import Button from '$lib/components/Button.svelte';
	import Input from '$lib/components/Input.svelte';
	import Card from '$lib/components/Card.svelte';
	import CardHeader from '$lib/components/CardHeader.svelte';
	import CardTitle from '$lib/components/CardTitle.svelte';
	import CardDescription from '$lib/components/CardDescription.svelte';
	import CardContent from '$lib/components/CardContent.svelte';

	let formData = $state({ email: '', password: '' });
	let isLoading = $state(false);
	let authState = $state({ user: null, isLoading: true });

	// Subscribe to auth changes
	onMount(() => {
		return auth.subscribe((state) => {
			authState = state;
			
			// Redirect if already logged in
			if (!state.isLoading && state.user) {
				goto('/dashboard');
			}
		});
	});

	async function handleSubmit(event: Event) {
		event.preventDefault();
		isLoading = true;

        const response = await api.post('/login', formData).finally(() => isLoading = false);

        notifications.success(response.message);

        // Update auth state
        auth.login(response.data.user);

        // Handle redirect
        if (browser) {
            const urlParams = new URLSearchParams(window.location.search);
            const urlRedirect = urlParams.get('rdr');
            const storedRedirect = localStorage.getItem('auth_redirect_url');

            // Clear stored redirect
            if (storedRedirect) {
                localStorage.removeItem('auth_redirect_url');
            }

            // Determine where to redirect
            const redirectTo = urlRedirect || storedRedirect;

            if (redirectTo && redirectTo !== '/login' && redirectTo !== '/signup' && redirectTo !== '/dashboard') {
                notifications.success('Welcome back! Redirecting to your previous page...');
                setTimeout(() => {
                    goto(redirectTo);
                }, 1000);
            } else {
                goto('/dashboard');
            }
        }
	}
</script>

<div class="min-h-screen flex items-center justify-center bg-background p-4">
	<Card class="w-full max-w-md">
		<CardHeader class="text-center">
			<CardTitle class="text-2xl font-serif font-black text-primary">Admin Dashboard</CardTitle>
			<CardDescription>Sign in to access your business management dashboard</CardDescription>
		</CardHeader>
		<CardContent>
			<form onsubmit={handleSubmit} class="space-y-4">
				<Input
					label="Email"
					name="email"
					type="email"
					placeholder="<EMAIL>"
					bind:formData
					required
					disabled={isLoading}
				/>

				<Input
					label="Password"
					name="password"
					type="password"
					placeholder="Enter your password"
					bind:formData
					required
					disabled={isLoading}
				/>

				<Button type="submit" class="w-full" loading={isLoading} loadingText="Logging in...">
					Login
				</Button>
			</form>
		</CardContent>
	</Card>
</div>
