<script lang="ts">
	import { goto } from '$app/navigation';
	import { auth } from '$lib/stores/auth.js';
	import { notifications } from '$lib/stores/notifications.js';
	import { api } from '$lib/api.js';
	import Button from '$lib/components/Button.svelte';
	import Input from '$lib/components/Input.svelte';
	import Card from '$lib/components/Card.svelte';

	let formData = $state({ name: '', email: '', password: '' });
	let isLoading = $state(false);

	async function handleSubmit(event: Event) {
		event.preventDefault();
		isLoading = true;

		try {
			const response = await api.post('/sys-arch/new-user', formData);
			
			notifications.success(response.message);
			goto('/login');
		} catch (error) {
			// Error handling is done in the API client
		} finally {
			isLoading = false;
		}
	}
</script>

<div class="min-h-screen flex items-center justify-center bg-background p-4">
	<Card
		class="w-full max-w-md"
		title="New System Admin"
		titleClass="text-2xl font-serif font-black text-primary"
		description="Create your admin account to get started"
		headerClass="text-center"
	>
		<form onsubmit={handleSubmit} class="space-y-4">
				<Input
					label="Name"
					name="name"
					type="text"
					placeholder="Your Name"
					bind:formData
					required
					disabled={isLoading}
				/>

				<Input
					label="Email"
					name="email"
					type="email"
					placeholder="<EMAIL>"
					bind:formData
					required
					disabled={isLoading}
				/>

				<Input
					label="Password"
					name="password"
					type="password"
					placeholder="Enter your password"
					bind:formData
					required
					disabled={isLoading}
				/>

				<Button type="submit" class="w-full" loading={isLoading} loadingText="Creating account...">
					Create Account
				</Button>

				<div class="text-center">
					<a href="/login" class="text-sm text-primary hover:underline">
						Already have an account? Sign in
					</a>
				</div>
			</form>
	</Card>
</div>
