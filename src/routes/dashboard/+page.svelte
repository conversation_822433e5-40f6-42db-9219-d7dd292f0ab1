<script lang="ts">
	import { onMount } from 'svelte';
	import { api } from '$lib/api.js';
	import { notifications } from '$lib/stores/notifications.js';
	import Card from '$lib/components/Card.svelte';
	import CardHeader from '$lib/components/CardHeader.svelte';
	import CardTitle from '$lib/components/CardTitle.svelte';
	import CardContent from '$lib/components/CardContent.svelte';
	import Button from '$lib/components/Button.svelte';

	let businesses = $state([]);
	let isLoading = $state(true);

	onMount(async () => {
		try {
			const response = await api.get('/sys-arch/businesses');
			businesses = response.data || [];
		} catch (error) {
			// Error handling is done in the API client
		} finally {
			isLoading = false;
		}
	});

	function getStatusColor(status: string) {
		switch (status) {
			case 'active': return 'bg-green-100 text-green-800';
			case 'inactive': return 'bg-red-100 text-red-800';
			case 'pending': return 'bg-yellow-100 text-yellow-800';
			default: return 'bg-gray-100 text-gray-800';
		}
	}

	function testNotifications() {
		notifications.success('Success notification!');
		notifications.error('Error notification!');
		notifications.warning('Warning notification!');
		notifications.info('Info notification!');
	}
</script>

<div class="space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight text-gray-900">Businesses</h1>
			<p class="mt-2 text-sm text-gray-700">Manage your business accounts and subscriptions</p>
		</div>
		<Button onclick={testNotifications}>Test Notifications</Button>
	</div>

	{#if isLoading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else}
		<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
			{#each businesses as business}
				<Card>
					<CardHeader>
						<CardTitle class="text-lg">{business.name}</CardTitle>
						<div class="flex items-center gap-2">
							<span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium {getStatusColor(business.status)}">
								{business.status}
							</span>
							<span class="text-sm text-gray-500">{business.subscriptionPlan}</span>
						</div>
					</CardHeader>
					<CardContent>
						<div class="space-y-2 text-sm">
							<div>
								<span class="font-medium">Email:</span>
								<span class="text-gray-600">{business.email}</span>
							</div>
							<div>
								<span class="font-medium">Contact:</span>
								<span class="text-gray-600">{business.contactName}</span>
							</div>
							<div>
								<span class="font-medium">Joined:</span>
								<span class="text-gray-600">{new Date(business.dateJoined).toLocaleDateString()}</span>
							</div>
							{#if business.lastLogin}
								<div>
									<span class="font-medium">Last Login:</span>
									<span class="text-gray-600">{new Date(business.lastLogin).toLocaleDateString()}</span>
								</div>
							{/if}
						</div>
					</CardContent>
				</Card>
			{/each}
		</div>
	{/if}
</div>
