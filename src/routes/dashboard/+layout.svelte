<script lang="ts">
    import {onMount} from 'svelte';
    import {goto} from '$app/navigation';
    import {page} from '$app/state';
    import {auth} from '$lib/stores/auth';
    import {api} from '$lib/api';
    import Button from '$lib/components/Button.svelte';
    import {Activity, Building2, Component, LogOut, Menu, Package, Package2, X} from '@lucide/svelte';

    let authState = $state({ user: null, isLoading: true });
	let isSidebarOpen = $state(false);

	// Subscribe to auth changes
	onMount(() => {
        return auth.subscribe((state) => {
            authState = state;

            // Redirect if not logged in and not loading
            if (!state.isLoading && !state.user) {
                goto('/login');
            }
        });
	});

	async function handleLogout() {
		await api.logout();
	}

	const navigation = [
		{
			name: 'Businesses',
			href: '/dashboard',
			icon: Building2,
		},
		{
			name: 'Plans',
			href: '/dashboard/plans',
			icon: Package,
		},
		{
			name: 'Products',
			href: '/dashboard/products',
			icon: Package2,
		},
		{
			name: 'Health Monitor',
			href: '/dashboard/health',
			icon: Activity,
		},
	];

	function isActive(href: string) {
		return page.url.pathname === href;
	}
</script>

{#if authState.isLoading}
	<div class="min-h-screen flex items-center justify-center">
		<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
	</div>
{:else if authState.user}
	<div class="min-h-screen bg-background">
		<!-- Mobile sidebar -->
		{#if isSidebarOpen}
			<div class="fixed inset-0 z-50 lg:hidden">
				<button class="fixed inset-0 bg-black/20" aria-label="Overlay" onclick={() => isSidebarOpen = false}></button>
				<div class="fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200">
					<div class="flex items-center justify-between p-4 border-b border-gray-200">
						<h2 class="text-lg font-serif font-bold text-gray-900">Admin Dashboard</h2>
						<Button variant="ghost" size="sm" onclick={() => isSidebarOpen = false}>
							<X class="h-4 w-4" />
						</Button>
					</div>
					<nav class="p-4 space-y-2">
						{#each navigation as item}
                            {@const Icon = item.icon}
							<a
								href={item.href}
								class="flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors {isActive(item.href)
									? 'bg-primary text-primary-foreground'
									: 'text-gray-700 hover:bg-gray-100'}"
								onclick={() => isSidebarOpen = false}
							>
                                <Icon class="h-4 w-4" />
								{item.name}
							</a>
						{/each}
					</nav>
				</div>
			</div>
		{/if}

		<!-- Desktop sidebar -->
		<div class="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-50 lg:block lg:w-64 lg:bg-white lg:border-r lg:border-gray-200">
			<div class="flex h-16 items-center px-6 border-b border-gray-200">
				<h2 class="text-lg font-serif font-bold text-gray-900">Admin Dashboard</h2>
			</div>
			<nav class="p-4 space-y-2">
				{#each navigation as item}
                    {@const Icon = item.icon}
					<a
						href={item.href}
						class="flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors {isActive(item.href)
							? 'bg-primary text-primary-foreground'
							: 'text-gray-700 hover:bg-gray-100'}"
					>
                        <Icon class="h-4 w-4" />
						{item.name}
					</a>
				{/each}
			</nav>
		</div>

		<!-- Main content -->
		<div class="lg:pl-64">
			<!-- Top bar -->
			<div class="sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
				<Button variant="ghost" size="sm" class="lg:hidden" onclick={() => isSidebarOpen = true}>
					<Menu class="h-4 w-4" />
				</Button>

				<div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
					<div class="flex flex-1"></div>
					<div class="flex items-center gap-x-4 lg:gap-x-6">
						<div class="flex items-center gap-x-2">
							<span class="text-sm font-medium text-gray-900">{authState.user.name}</span>
							<Button variant="ghost" size="sm" onclick={handleLogout} class="gap-2">
								<LogOut class="h-4 w-4" />
								Logout
							</Button>
						</div>
					</div>
				</div>
			</div>

			<!-- Page content -->
			<main class="py-10">
				<div class="px-4 sm:px-6 lg:px-8">
                    <slot />
				</div>
			</main>
		</div>
	</div>
{/if}
