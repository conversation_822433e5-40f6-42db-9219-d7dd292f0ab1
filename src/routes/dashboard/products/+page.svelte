<script lang="ts">
	import { onMount } from 'svelte';
	import { notifications } from '$lib/stores/notifications.js';
	import Card from '$lib/components/Card.svelte';
	import Button from '$lib/components/Button.svelte';
    import Input from '$lib/components/Input.svelte';
    import {api} from "$lib/api";
    import {modals} from "$lib/stores/modals";

    let products = $state([
        {
            id: '1',
            name: 'Basic Plan',
            description: 'Essential features for small businesses',
            price: '$29/month',
            features: ['Up to 10 users', 'Basic analytics', 'Email support'],
            status: 'active'
        },
        {
            id: '2',
            name: 'Pro Plan',
            description: 'Advanced features for growing businesses',
            price: '$79/month',
            features: ['Up to 50 users', 'Advanced analytics', 'Priority support', 'API access'],
            status: 'active'
        },
        {
            id: '3',
            name: 'Enterprise Plan',
            description: 'Full-featured solution for large organizations',
            price: '$199/month',
            features: ['Unlimited users', 'Custom analytics', '24/7 support', 'Full API access', 'Custom integrations'],
            status: 'active'
        }
    ]);
    let bricks = $state([]);

    onMount(async () => {
        const response = await api.get('/sys-arch/business/bricks');
        bricks = response.data || [];
    });

    function newProducts() {
        modals.open({
            title: 'Add New Product',
            content: () => (
                <>
                    <form>
                        <Input
                            label="Email"
                            name="email"
                            type="email"
                            placeholder="<EMAIL>"
                            bind:formData
                            required
                            disabled={isLoading}
                        />
                    </form>
                </>
            )
        })
    }

	function getStatusColor(status: string) {
		switch (status) {
			case 'active': return 'bg-green-100 text-green-800';
			case 'inactive': return 'bg-red-100 text-red-800';
			case 'draft': return 'bg-yellow-100 text-yellow-800';
			default: return 'bg-gray-100 text-gray-800';
		}
	}

	function handleEdit(productId: string) {
		notifications.info(`Edit product ${productId} - Feature coming soon!`);
	}

	function handleDelete(productId: string) {
		notifications.warning(`Delete product ${productId} - Feature coming soon!`);
	}
</script>

<div class="space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight text-gray-900">Products</h1>
			<p class="mt-2 text-sm text-gray-700">Manage your product catalog and pricing plans</p>
		</div>
		<Button onclick={newProducts}>
			Add Product
		</Button>
	</div>

	<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
		{#each products as product}
			<Card title={product.name} titleClass="text-lg" description={product.description}>
				<div class="flex items-center justify-between mb-4">
					<span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium {getStatusColor(product.status)}">
						{product.status}
					</span>
				</div>
				<div class="space-y-4">
					<div class="text-2xl font-bold text-primary">{product.price}</div>

					<div>
						<h4 class="font-medium text-sm mb-2">Features:</h4>
						<ul class="space-y-1">
							{#each product.features as feature}
								<li class="text-sm text-gray-600 flex items-center">
									<span class="w-1.5 h-1.5 bg-primary rounded-full mr-2"></span>
									{feature}
								</li>
							{/each}
						</ul>
					</div>

					<div class="flex gap-2 pt-2">
						<Button size="sm" variant="outline" onclick={() => handleEdit(product.id)}>
							Edit
						</Button>
						<Button size="sm" variant="destructive" onclick={() => handleDelete(product.id)}>
							Delete
						</Button>
					</div>
				</div>
			</Card>
		{/each}
	</div>
</div>
