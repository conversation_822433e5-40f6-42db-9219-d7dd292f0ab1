<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { auth } from '$lib/stores/auth.js';
	import { LoaderCircle } from '@lucide/svelte';

	let authState = $state({ user: null, isLoading: true });

	// Subscribe to auth changes
	onMount(() => {
		const unsubscribe = auth.subscribe((state) => {
			authState = state;
			
			if (!state.isLoading) {
				if (state.user) {
					goto('/dashboard');
				} else {
					goto('/login');
				}
			}
		});

		return unsubscribe;
	});
</script>

<div class="min-h-screen flex items-center justify-center bg-background">
	<LoaderCircle class="h-8 w-8 animate-spin text-primary" />
</div>
