{"name": "elevator-admin-svelte", "version": "0.0.1", "private": true, "scripts": {"build": "vite build", "dev": "vite dev", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@lucide/svelte": "^0.543.0", "@sveltejs/adapter-auto": "^6.1.0", "@sveltejs/kit": "^2.37.1", "@sveltejs/vite-plugin-svelte": "^6.1.4", "@tailwindcss/vite": "^4.1.13", "clsx": "^2.1.1", "svelte": "^5.38.7", "svelte-check": "^4.3.1", "tailwindcss": "^4.1.13", "typescript": "^5.9.2", "vite": "^7.1.5"}, "type": "module"}