# Elevator Admin - SvelteKit Version

A minimal, performant admin dashboard built with SvelteKit, TypeScript, and Tailwind CSS.

## ✨ Features

- **🚀 Minimal & Fast** - Built with SvelteKit for optimal performance
- **🔐 Authentication** - Complete auth system with session management
- **📱 Responsive** - Mobile-first design with sidebar navigation
- **🎨 Modern UI** - Clean design with Tailwind CSS
- **🔔 Notifications** - Toast notification system (replaces alert())
- **📝 Forms** - Auto-serializing form inputs with validation
- **🛡️ TypeScript** - Full type safety throughout
- **⚡ Session Handling** - Automatic 909 response handling and redirects

## 🏗️ Architecture

### Core Systems
- **Auth Store** (`/lib/stores/auth.ts`) - Reactive authentication state
- **Notifications** (`/lib/stores/notifications.ts`) - Global notification system
- **API Client** (`/lib/api.ts`) - Centralized HTTP client with error handling
- **Components** (`/lib/components/`) - Reusable UI components

### Key Features
- **Session Persistence** - Survives page refreshes
- **Auto-redirect** - Returns users to previous page after login
- **Error Handling** - Centralized API error management
- **Form Management** - Automatic form data serialization

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ (compatible with older versions unlike Next.js)
- npm, yarn, or pnpm

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   ```
   http://localhost:5173
   ```

### Default Login
- **Email:** <EMAIL>
- **Password:** password

## 📁 Project Structure

```
src/
├── lib/
│   ├── components/          # Reusable UI components
│   │   ├── Button.svelte
│   │   ├── Input.svelte
│   │   ├── Card.svelte
│   │   └── NotificationContainer.svelte
│   ├── stores/              # Svelte stores
│   │   ├── auth.ts          # Authentication state
│   │   └── notifications.ts # Notification system
│   ├── api.ts              # HTTP client
│   └── utils.ts            # Utility functions
├── routes/
│   ├── +layout.svelte      # Root layout
│   ├── +page.svelte        # Home page (redirects)
│   ├── login/              # Login page
│   ├── signup/             # Signup page
│   └── dashboard/          # Protected dashboard
│       ├── +layout.svelte  # Dashboard layout
│       ├── +page.svelte    # Businesses page
│       └── products/       # Products page
└── app.html               # HTML template
```

## 🎯 Usage Examples

### Notifications
```typescript
import { notifications } from '$lib/stores/notifications.js';

// Simple notifications
notifications.success('Operation successful!');
notifications.error('Something went wrong');
notifications.warning('Please check your input');
notifications.info('FYI: This is important');

// Replace alert()
notifications.alert('This replaces alert()!');

// With actions
notifications.success('File uploaded!', {
  action: {
    label: 'View',
    onClick: () => goto('/files')
  }
});
```

### Forms
```svelte
<script>
  let formData = $state({ email: '', password: '' });
  
  async function handleSubmit() {
    const response = await api.post('/auth/login', formData);
    // formData automatically contains all input values
  }
</script>

<form onsubmit={handleSubmit}>
  <Input name="email" bind:formData />
  <Input name="password" type="password" bind:formData />
  <Button type="submit">Login</Button>
</form>
```

### API Calls
```typescript
import { api } from '$lib/api.js';

// All methods available
const data = await api.get('/businesses');
const result = await api.post('/users', userData);
const updated = await api.put('/users/1', changes);
const patched = await api.patch('/users/1', partialChanges);
await api.delete('/users/1');

// Automatic error handling and notifications
// 909 responses automatically trigger logout and redirect
```

### Authentication
```typescript
import { auth } from '$lib/stores/auth.js';

// Login
auth.login(userData);

// Logout
auth.logout();

// Check auth state
$auth.user; // Current user or null
$auth.isLoading; // Loading state
```

## 🔧 Configuration

### API Base URL
Update the base URL in `/lib/api.ts`:
```typescript
constructor() {
  this.baseUrl = '/api/v1/e-ad'; // Change this
}
```

### Styling
Customize colors in `/src/app.css` CSS variables.

## 🚀 Deployment

### Build for production:
```bash
npm run build
```

### Preview production build:
```bash
npm run preview
```

## 📊 Performance Benefits vs Next.js

- **Smaller bundle size** - No React overhead
- **Faster hydration** - Svelte compiles to vanilla JS
- **Better Core Web Vitals** - Optimized by default
- **Simpler deployment** - Static files or Node.js
- **Less memory usage** - No virtual DOM
- **Faster development** - Instant HMR

## 🔄 Migration from Next.js

This SvelteKit version provides the same functionality as your Next.js app:

- ✅ **Authentication system** - Complete with session management
- ✅ **Form handling** - Auto-serializing inputs
- ✅ **Notifications** - Toast system replacing alert()
- ✅ **API client** - Centralized HTTP handling
- ✅ **Responsive design** - Mobile-first dashboard
- ✅ **Session persistence** - Survives page refreshes
- ✅ **Error handling** - 909 response handling
- ✅ **TypeScript** - Full type safety

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - feel free to use this project for your own applications.

---

**Built with ❤️ using SvelteKit for maximum performance and minimal complexity.**
